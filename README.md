# QIPLUS Checkout Frontend

## Overview

The QIPLUS Checkout frontend is a modern, responsive web application built to provide a seamless checkout experience for users purchasing QIPLUS subscription plans. The application handles the entire checkout flow, from plan selection to payment processing, with support for multiple payment methods and subscription management.

## Features

- **Plan Selection**: Browse and select from various subscription plans with monthly and annual billing options
- **Multi-step Checkout Process**: User-friendly step-by-step checkout flow
- **Multiple Payment Methods**: Support for credit card, PIX, and bank slip payments
- **Subscription Management**: View and manage active subscriptions
- **Invoice Tracking**: Access and download invoice history
- **Responsive Design**: Optimized for all device sizes
- **Dark Mode Support**: Toggle between light and dark themes
- **Live Support Chat**: Get assistance during the checkout process

## Technology Stack

- **Framework**: React 18
- **Build Tool**: Vite
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Form Handling**: React Hook Form with Zod validation
- **State Management**: React Context API
- **Routing**: React Router
- **API Communication**: Axios, TanStack Query
- **Authentication**: Firebase Authentication
- **Payment Processing**: Integration with payment gateways
- **Animation**: Framer Motion

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>

# Navigate to the frontend directory
cd qiplus-checkout/frontend

# Install dependencies
npm install
# or
yarn install
```

### Environment Setup

Create a `.env` file in the frontend directory based on the `.env.example` template:

```bash
# Firebase configuration
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_AUTH_DOMAIN=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_STORAGE_BUCKET=
VITE_FIREBASE_MESSAGING_SENDER_ID=
VITE_FIREBASE_APP_ID=
VITE_FIREBASE_SERVICE_ACCOUNT=

# Backend API
VITE_API_URL=
VITE_SYSTEM_URL_REDIRECT=
```

### Development

```bash
# Start the development server
npm run dev
# or
yarn dev
```

The application will be available at `http://localhost:8080`.

### Building for Production

```bash
# Build the application
npm run build
# or
yarn build
```

## Project Structure

- `/src`: Source code
  - `/components`: Reusable UI components
  - `/contexts`: React context providers
  - `/hooks`: Custom React hooks
  - `/lib`: Utility functions
  - `/models`: Data models
  - `/pages`: Application pages
  - `/repositories`: Data access layer
  - `/services`: API services
  - `/types`: TypeScript type definitions

## Integration with Backend

The frontend communicates with the QIPLUS Checkout backend API for:
- User authentication
- Plan retrieval and management
- Payment processing
- Subscription management
- Invoice generation and retrieval

## Contributing

1. Create a feature branch from the main branch
2. Make your changes
3. Submit a pull request

## License

Proprietary - All rights reserved
