import { Subscription } from "@/models/Subscription";
import { PixPaymentValues } from "./account";

export enum PaymentStatus {
  PROCESSING = "processing",
  SUCCESS = "success",
  ERROR = "error",
  STOPPED = "stopped",
  WAITING = "waiting",
}

export type PaymentProcessStatus = {
  status: PaymentStatus;
  error?: string;
  subscription?: Subscription;
  processing: boolean;
  isUpdated?: boolean;
  skipPayment?: boolean;
  pix?: PixPaymentValues;
};
