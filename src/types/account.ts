import { PagarmeData } from "./pagarme";
import { Plan } from "./plan-types";

export interface Account {
  ID: string;
  accountId: string;
  active: boolean;
  actions: AccountActions;
  author: string;
  billing_data: BillingData;
  config: AccountConfig;
  content: string;
  createdAt: number;
  data: AccountData;
  edit_url: string;
  id: string;
  images: AccountImages;
  imageSizes: ImageSize[];
  keywords: string[];
  levels: AccountLevels;
  locale: string;
  logs: AccountLogs;
  modules: AccountModules;
  modified: string;
  owner: string;
  pagarme: PagarmeData;
  parentId: string;
  plan: Plan;
  planId: string;
  post_type: string;
  settings: AccountSettings;
  slug: string;
  status: string;
  thumbnail: string;
  title: string;
  type: string;
  uid: string;
  updatedAt: number;
  updatedBy: string;
  url: string;
  values: AccountValues;
  payment_status?: AccountPayment_status;
  payment_upgrade_status?: AccountPayment_status;
  payment?: {
    data: {
      amount: number;
    };
  };
}

export interface PixPaymentValues {
  qrCode: string;
  amount: number;
  identifier: string;
  expiresAt: string;
}

export interface AccountPayment_status {
  status: "paid" | "failed" | "pending";
  pix?: PixPaymentValues;
  gateway: string;
  subscription_id: string;
  error_message: string;
  error: boolean;
}

interface AccountActions {
  ACTION_DEAL_UPDATE: boolean;
  ACTION_GO_TO_ACTION: boolean;
  ACTION_EMAIL: boolean;
  ACTION_TEAMS: boolean;
  ACTION_REMOVE_TAGS: boolean;
  ACTION_DEALS_TAGS: boolean;
  ACTION_LOG: boolean;
  ACTION_USER_TASKLIST: boolean;
  ACTION_STORE: boolean;
  ACTION_GO_TO_AUTOMATION: boolean;
  ACTION_TAGS: boolean;
  ACTION_CREATE_TICKET: boolean;
  ACTION_SCHEDULE: boolean;
  ACTION_DEAL_TASKLIST: boolean;
  ACTION_PROFILE: boolean;
  ACTION_NOTIFICATE_MANAGER: boolean;
  ACTION_REMOVE_DEALS_TAGS: boolean;
  ACTION_FUNNEL_SWITCHER: boolean;
  ACTION_SCORE: boolean;
  ACTION_TIMER: boolean;
  ACTION_NOTIFICATE_SELLER: boolean;
  ACTION_WEBHOOK: boolean;
  ACTION_DEAL: boolean;
  ACTION_EVALUATE: boolean;
  ACTION_SEGMENTATIONS: boolean;
  ACTION_EVENT: boolean;
  ACTION_TEAM_TASKLIST: boolean;
  ACTION_DEAL_UPDATE_IN_FUNNEL: boolean;
  ACTION_DEAL_WEBHOOK: boolean;
  ACTION_SET_MANAGER: boolean;
  ACTION_SET_SELLER: boolean;
  ACTION_CONTRACT: boolean;
}

interface BillingData {
  customer: {
    type: string;
    document_number: string;
    phone: {
      ddi: string;
      number: string;
      ddd: string;
    };
    document_type: string;
    country: string;
    external_id: string;
    name: string;
    email: string;
  };
  address: {
    street: string;
    state: string;
    city: string;
    neighborhood: string;
    zipcode: string;
    object: string;
    country: string;
    complementary: string;
    id: number;
    street_number: string;
  };
  phone: {
    id: number;
    ddi: number;
    ddd: number;
    number: string;
    object: string;
  };
}

interface AccountConfig {
  events_included: number;
  automations_included: number;
  billing: boolean;
  mailing_qiplus_disabled: boolean;
  emails_included: number;
  plan_type: string;
  email_option: string;
  funnels_included: number;
  landing_pages_included: number;
  payment_method: string;
  smtp_enabled: boolean;
  mailboxes_included: number;
  forms_included: number;
  trial_days: number;
  qiusers_included: number;
  implementation_charge: boolean;
  shotx_included: number;
  custom_plan: boolean;
  campaigns_included: number;
  emails_towards_base: number;
}

interface AccountData {
  contacts: string;
  contacts_min: number;
  price: number;
  yearly_value: string;
  contacts_max: string;
  monthly_value: string;
}

interface AccountImages {
  mainLogo: string;
}

interface ImageSize {
  filename: { stringValue: string };
  label: { stringValue: string };
}

interface AccountLevels {
  landing_pages: number;
  events: number;
  barcode_products: number;
  forms: number;
  mailing: number;
  campaigns: number;
  automations: number;
  raffles: number;
  live_qiplus: number;
  tickets: number;
  trackings: number;
  funnels: number;
  questionnaires: number;
}

interface AccountLogs {
  updated: {
    user: string;
    operator_id: string;
    date: string;
  };
}

interface AccountModules {
  trackings: boolean;
  prizes: number;
  events: boolean;
  funnels: boolean;
  tickets: boolean;
  campaigns: boolean;
  qiusers: boolean;
  contracts: boolean;
  automations: boolean;
  forms: boolean;
  barcode_products: boolean;
  live_qiplus: boolean;
  questionnaires: boolean;
  landing_pages: boolean;
  shotx: boolean;
  mailing: boolean;
  mailboxes: boolean;
  raffles: boolean;
  chats: number;
}

interface AccountSettings {
  brand: {
    name: string;
    slogan: string;
  };
  emails: {
    footer: string;
    mailboxId: string;
    smtp_integration: string;
    address: string;
    smtp: string;
    fromName: string;
  };
  privacy: {
    terms_btnColor: string;
    privacy_btnBgColor: string;
    privacyUrl: string;
    privacyText: string;
    terms_bgColor: string;
    termsUrl: string;
    privacy_btnColor: string;
    privacyType: string;
    terms_color: string;
    privacy_bgColor: string;
    privacy_color: string;
    termsType: string;
    termsText: string;
    terms_btnBgColor: string;
  };
  styles: {
    header_color: string;
    slogan_color: string;
  };
}

interface AccountValues {
  extra_campaigns_monthly: number;
  extra_user_monthly: number;
  extra_events_yearly: number;
  extra_mailboxes_monthly: number;
  extra_user_yearly: string;
  extra_campaigns_yearly: number;
  extra_qiusers_yearly: number;
  implementation: number;
  extra_shotx_monthly: number;
  extra_mailboxes_yearly: number;
  extra_automations_monthly: number;
  extra_forms_monthly: number;
  extra_events_monthly: number;
  extra_landing_pages_yearly: number;
  extra_forms_yearly: number;
  extra_shotx_yearly: number;
  extra_automations_yearly: number;
  extra_funnels_monthly: number;
  extra_event: string;
  extra_landing_pages_monthly: number;
  extra_funnels_yearly: number;
  extra_qiusers_monthly: number;
}
