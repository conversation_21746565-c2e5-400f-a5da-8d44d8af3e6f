export interface PagarmePhone {
  ddd: string;
  ddi: string;
  id: number;
  number: string;
  object: string;
}

export interface PagarmePlan {
  amount: number;
  charges: null;
  color: null;
  date_created: string;
  days: number;
  id: number;
  installments: number;
  invoice_reminder: null;
  name: string;
  object: string;
  payment_deadline_charges_interval: number;
  payment_methods: string[];
  trial_days: number;
  postback_url: string;
  settled_charges: null;
  soft_descriptor: null;
  status: string;
}

export interface PagarmeMetadata {
  accountId: string;
  implementation: string;
  implementation_id: string;
  parcelas: string;
  uid: string;
  object: string;
  payment_method: string;
}

export interface PagarmeSubscription {
  brand_transaction_id: null;
  card_brand: string;
  card_last_digits: string;
  charges: number;
  current_period_end: string;
  current_period_start: string;
  date_created: string;
  date_updated: string;
  fine: Record<string, never>;
  id: number;
  interest: Record<string, never>;
  manage_token: string;
  manage_url: string;
  metadata: PagarmeMetadata;
  phone: PagarmePhone;
  plan: PagarmePlan;
  subscription_id: number;
}

export interface PagarmeData {
  days: number;
  installments: number;
  parcelas: number;
  payment_method: string;
  plan_id: number;
  recurrency: string;
  status: string;
  subscription: PagarmeSubscription;
  parentId: string;
  customerId: string;
}
