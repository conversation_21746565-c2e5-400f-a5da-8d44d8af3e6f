import { db } from '@/config/firebase';
import { PlanModel } from '@/models/Plan';
import { Plan } from '@/types/plan-types';
import { collection, getDocs } from 'firebase/firestore';


class PlansRepository {
  private readonly COLLECTION_NAME = 'qiplus-plans';

  async getPlans(): Promise<Plan[]> {
    try {
      const plansCollection = collection(db, this.COLLECTION_NAME)
      const plansSnapshot = await getDocs(plansCollection);
      const plans = plansSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          status: data.status,
          order: data.config.order ?? 0,
          ...data
        };
      });

      return plans.filter(plan => plan.status === 'publish')
        .sort((a, b) => a.order - b.order)
        .map(plan => PlanModel.parse(plan));
    } catch (error) {
      console.error('Erro ao buscar planos:', error);
      return [];
    }
  }
}

export const plansRepository = new PlansRepository();
