import { CustomFeature } from "@/types/plan-types";
import { ApiService } from "./api";

export class PlansService extends ApiService {
  async getPlans(data: {
    leadsCount: number;
    additionals: Array<any>;
    isYearly: boolean;
    customFeatures: Array<CustomFeature>;
    billingDay: number;
  }) {
    return await this.axios.post(`/upgrade/plans`, data).then((res) => {
      return res.data;
    });
  }
}

export const plansService = new PlansService();
