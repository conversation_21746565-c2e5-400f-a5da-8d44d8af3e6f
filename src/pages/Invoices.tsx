import { useEffect, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import { subscriptionService } from "@/services/subscription";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { cn, formatCurrency } from "@/lib/utils";
import { formatCycleDate } from "@/lib/invoice.utils";
import { Invoice } from "@/types/invoice.types";
import InvoiceTable from "@/components/invoices/InvoiceTable";
import CancelModal from "@/components/invoices/CancelModal";
import SubscriptionInfo from "@/components/invoices/SubscriptionInfo";
import { SubscriptionHeader } from "@/components/SubascriptionHeader";
import { t } from "@/lib/translations.helper";
import { QISubscription } from "@/types/backend/qiplus.types";

export default function Invoices() {
  const { subscriptionId } = useParams();
  const { state } = useLocation();
  const { payment, hasActiveSubscription = true } = state || {};

  const [loading, setLoading] = useState(true);
  const [subscription, setSubscription] = useState<QISubscription | null>(payment || null);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isCancelledSubscription, setIsCancelledSubscription] = useState(false);

  // Estado para controlar o modal de confirmação
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;
  const { toast } = useToast();
  const ArrowButton = ({
    onClick,
    direction,
  }: {
    onClick?: () => void;
    direction: "left" | "right";
  }) => (
    <button
      onClick={onClick}
      className={`absolute top-1/2 transform -translate-y-1/2 z-10 ${direction === "left" ? "-left-4" : "-right-4"
        } text-[#0071e2] bg-transparent p-1 hover:opacity-75 dark:text-white`}
    >
      {direction === "left" ? (
        <ChevronLeft size={18} />
      ) : (
        <ChevronRight size={18} />
      )}
    </button>
  );

  useEffect(() => {
    if (!subscription) return;
    if (subscription) {
      const subscriptionCanceled = subscription?.status === "canceled" || subscription?.scheduledAction === "cancel";
      setIsCancelledSubscription(subscriptionCanceled);
    }
  }, [subscription]);

  useEffect(() => {
    async function fetchInvoiceData() {
      if (!subscriptionId) return;

      try {
        const response = await subscriptionService.getInvoices(
          subscriptionId,
          !payment
        );
        setInvoices(response?.invoices || []);
        if (!payment) {
          setSubscription(response?.subscription || null);
        }
      } catch (error) {
        toast({
          title: "Erro ao carregar faturas",
          description:
            "Não foi possível carregar as faturas. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    }
    fetchInvoiceData();
  }, [subscriptionId, toast]); // invoices não é necessário como dependência pois é apenas atualizado dentro do efeito

  /**
   * Open boleto URL in a new window
   */
  const handleOpenBoleto = async (qr_code: string) => {
    try {
      // const url = await subscriptionService.getUrlBoleto(invoiceId);
      if (qr_code) {
        window.open(qr_code, "_blank");
      }
    } catch (error) {
      toast({
        title: "Erro ao abrir boleto",
        description:
          "Não foi possível abrir o boleto. Tente novamente mais tarde.",
        variant: "destructive",
      });
    }
  };

  /**
   * Handle page change for pagination
   */
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const sliderSettings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 2,
    slidesToScroll: 1,
    nextArrow: <ArrowButton direction="right" />,
    prevArrow: <ArrowButton direction="left" />,
    responsive: [
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  /**
   * Cancel subscription
   */
  const handleCancelSubscription = async () => {
    if (!subscriptionId) return;

    try {
      const subscription =
        await subscriptionService.cancelSubscription(subscriptionId);
      setSubscription(subscription);

      toast({
        title: "Cancelamento aplicado!",
        description: "Cancelamento realizado com sucesso!",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: "Cancelamento negado!",
        description:
          "Algo aconteceu ao tentar realizar o cancelamento, por favor tente mais tarde. Se persistir, entre em contato com o suporte.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setShowCancelModal(false);
    }
  };



  // Calculate total pages for pagination
  const totalPages = Math.ceil(invoices.length / itemsPerPage);
  const isActive = subscription?.status === "active";
  const isPending = subscription?.status === "pending";
  const plan = subscription?.items?.find((item) => item.type === "plan");
  const additionalPlanItems = subscription?.items?.filter((item) => item.type !== "plan" && item.totalPrice > 0);
  // Prepare info card data
  const infoCardData = [
    {
      label: "Plano",
      value: plan?.name || "-",
      append: subscription?.billingInterval && <span className="text-xs text-muted-foreground">
        &nbsp;
        ({t(`interval.${subscription?.billingInterval}`)})
      </span>
    },
    {
      label: isCancelledSubscription
        ? "Seu acesso está disponível até:"
        : "Próximo Vencimento",
      value: formatCycleDate(subscription?.nextBillingDate) || "",
    },
    {
      label: "Valor",
      value: `${formatCurrency(plan?.totalPrice || 0)}`,
      append: <span >
        <span className="text-xs text-muted-foreground">
          /{t(`interval.${subscription?.billingInterval}`)}
        </span>
        <div className="text-xs text-muted-foreground">
          * não incluso os adicionais
        </div>
      </span>
    },
    {
      label: "Status",
      value: isActive ? (
        <span className={cn(
          "text-xs font-medium me-2 px-2.5 py-1 rounded-md", subscription?.status === "active" && "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300")}>
          {t(`status.${subscription?.status}`) || "-"}
        </span>
      ) : (
        <div className="flex items-center gap-2 justify-between">
          <span className="text-red-500">{t(`status.${subscription?.status}`)}</span>
        </div>
      ),
      append: isPending && (
        <span className="text-xs text-muted-foreground">
          Aguardando pagamento
        </span>
      )
    },
  ];

  return (
    <div className="min-h-screen mb-[5rem] px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <SubscriptionHeader />
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Faturas</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Visualize todas as suas faturas
          </p>
        </div>

        <div className="mb-6">
          <Button
            type="button"
            onClick={() => window.history.back()}
            variant="ghost"
          >
            <ChevronLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
        </div>

        {/* Subscription Info Section */}
        <SubscriptionInfo
          subscription={subscription}
          loading={loading}
          isActive={isActive}
          infoCardData={infoCardData}
          additionalPlanItems={additionalPlanItems}
          onCancelClick={() => setShowCancelModal(true)}
          sliderSettings={sliderSettings}
        />

        {/* Invoices Table */}
        <InvoiceTable
          invoices={invoices}
          loading={loading}
          subscriptionInterval={subscription?.billingInterval || ""}
          onOpenBoleto={handleOpenBoleto}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          itemsPerPage={itemsPerPage}
        />

        {/* Cancel Confirmation Modal */}
        {showCancelModal && (
          <CancelModal
            onCancel={handleCancelSubscription}
            onClose={() => setShowCancelModal(false)}
            nextBillingDate={subscription?.nextBillingDate || ""}
          />
        )}
      </div>
    </div>
  );
}
