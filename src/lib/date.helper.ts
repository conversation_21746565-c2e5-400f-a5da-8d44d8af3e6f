import { Timestamp } from "firebase/firestore";

export class DateHelper {
  /**
   * Converte uma data JavaScript para Timestamp do Firebase
   * @param date Data JavaScript ou string de data ISO
   * @returns Firebase Timestamp
   */
  static toFirebaseTimestamp(date: Date | string | number): Timestamp {
    if (typeof date === "string") {
      return Timestamp.fromDate(new Date(date));
    }

    if (typeof date === "number") {
      return Timestamp.fromMillis(date);
    }

    return Timestamp.fromDate(date);
  }

  /**
   * Converte um Timestamp do Firebase para Date do JavaScript
   * @param timestamp Firebase Timestamp
   * @returns JavaScript Date
   */
  static fromFirebaseTimestamp(timestamp: Timestamp): Date {
    return timestamp.toDate();
  }

  /**
   * Converte um objeto com datas para um objeto com Timestamps do Firebase
   * @param obj Objeto contendo datas
   * @returns Objeto com as datas convertidas para Timestamp
   */
  static convertDatesToTimestamps<T extends Record<string, unknown>>(
    obj: T
  ): T {
    const result = { ...obj };

    for (const key in result) {
      const value = result[key] as unknown;

      if (value instanceof Date) {
        (result[key] as unknown) = this.toFirebaseTimestamp(value);
      } else if (value && typeof value === "object") {
        (result[key] as unknown) = this.convertDatesToTimestamps(
          value as Record<string, unknown>
        );
      }
    }

    return result;
  }

  /**
   * Converte um objeto com Timestamps do Firebase para um objeto com Dates do JavaScript
   * @param obj Objeto contendo Timestamps
   * @returns Objeto com os Timestamps convertidos para Date
   */
  static convertTimestampsToDates<T extends Record<string, unknown>>(
    obj: T
  ): T {
    const result = { ...obj };

    for (const key in result) {
      const value = result[key] as unknown;

      if (value instanceof Timestamp) {
        (result[key] as unknown) = this.fromFirebaseTimestamp(value);
      } else if (value && typeof value === "object") {
        (result[key] as unknown) = this.convertTimestampsToDates(
          value as Record<string, unknown>
        );
      }
    }

    return result;
  }


  toUserLocale(date: Date | string | number): string {
    const userLocale = navigator.language;
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return new Intl.DateTimeFormat(userLocale, options).format(
      typeof date === "string" ? new Date(date) : date
    );
  }
}
