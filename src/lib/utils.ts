import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Format a monetary value from cents to currency display
 * @param cents Value in cents
 * @returns Formatted currency string
 */
export function formatCurrency(cents: number) {
  return new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(cents / 100)
}

/**
 * Format a monetary value for display, handling yearly values
 * @param cents Value in cents
 * @param yearly Whether the value is yearly
 * @returns Formatted currency string
 */
export function formatCurrencyToShow(cents: number, yearly = false) {
  if (cents === 0) return "<PERSON><PERSON><PERSON><PERSON>"
  if (yearly) {
    return formatCurrency(cents / 12)
  }
  return formatCurrency(cents)
}

/**
 * Calculate yearly discount in cents
 * @param valueCents Value in cents
 * @param discountCents Discount in cents
 * @returns Discounted value in cents
 */
export function calculateYearlyDiscount(valueCents: number, discountCents: number) {
  return valueCents - discountCents
}

/**
 * Calculate yearly discount percentage
 * @param valueCents Value in cents
 * @param discountCents Discount in cents
 * @returns Discount percentage
 */
export function calculateYearlyDiscountPercentage(valueCents: number, discountCents: number) {
  return ((valueCents - discountCents) / valueCents) * 100
}

/**
 * Calculate discount percentage
 * @param valueCents Value in cents
 * @param discountCents Discount in cents
 * @returns Discount percentage
 */
export function calculateDiscountPercentage(valueCents: number, discountCents: number) {
  return (discountCents / valueCents) * 100
}

export const replaceUndefined = (data: Array<object> | object, replacement = null) => {
  if (Array.isArray(data)) {
    return data.map((item: object) => replaceUndefined(item, replacement));
  }
  if (typeof data === "object") {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [
        key,
        value === undefined ? replacement : replaceUndefined(value, replacement),
      ])
    );
  }
  return data;
};

export const validateCPF = (cpf: string) => {
  cpf = cpf.replace(/[^\d]/g, '');

  if (cpf.length !== 11) return false;
  if (/^(\d)\1+$/.test(cpf)) return false;

  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cpf.charAt(i)) * (10 - i);
  }
  let digit = 11 - (sum % 11);
  if (digit >= 10) digit = 0;
  if (digit !== parseInt(cpf.charAt(9))) return false;

  sum = 0;
  for (let i = 0; i < 10; i++) {
    sum += parseInt(cpf.charAt(i)) * (11 - i);
  }
  digit = 11 - (sum % 11);
  if (digit >= 10) digit = 0;
  if (digit !== parseInt(cpf.charAt(10))) return false;

  return true;
};


export const validateCNPJ = (cnpj: string) => {
  cnpj = cnpj.replace(/[^\d]/g, '');

  if (cnpj.length !== 14) return false;
  if (/^(\d)\1+$/.test(cnpj)) return false;

  let size = cnpj.length - 2;
  let numbers = cnpj.substring(0, size);
  const digits = cnpj.substring(size);
  let sum = 0;
  let pos = size - 7;

  for (let i = size; i >= 1; i--) {
    sum += parseInt(numbers.charAt(size - i)) * pos--;
    if (pos < 2) pos = 9;
  }

  let result = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (result !== parseInt(digits.charAt(0))) return false;

  size = size + 1;
  numbers = cnpj.substring(0, size);
  sum = 0;
  pos = size - 7;

  for (let i = size; i >= 1; i--) {
    sum += parseInt(numbers.charAt(size - i)) * pos--;
    if (pos < 2) pos = 9;
  }

  result = sum % 11 < 2 ? 0 : 11 - (sum % 11);
  if (result !== parseInt(digits.charAt(1))) return false;

  return true;
};

export const maskCPF = (cpf: string) => {
  return cpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};

export const maskCNPJ = (cnpj: string) => {
  return cnpj.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};

function luhnCheck(cardNumber) {
  let sum = 0;
  for (let i = 0; i < cardNumber.length; i++) {
    let digit = parseInt(cardNumber[cardNumber.length - 1 - i], 10);
    if (i % 2 === 1) {
      digit *= 2;
      if (digit > 9) {
        digit = digit - 9;
      }
    }
    sum += digit;
  }
  return sum % 10 === 0;
}

export function isValidCreditCardNumber(cardNumber) {
  const creditCardRegex = /^(?:4[0-9]{12}(?:[0-9]{3})?|5[1-5][0-9]{14}|6(?:011|5[0-9]{2})[0-9]{12}|3[47][0-9]{13}|3(?:0[0-5]|[68][0-9])[0-9]{11}|(?:2131|1800|35\d{3})\d{11})$/;
  return creditCardRegex.test(cardNumber) && luhnCheck(cardNumber);
}
