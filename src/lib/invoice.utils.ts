import { formatDate } from "date-fns";
import { ptBR } from "date-fns/locale";

/**
 * Format date to Brazilian format
 * @param date Date string to format
 * @returns Formatted date string
 */
export const formatCycleDate = (date: string): string => {
  if (!date) return "";
  return formatDate(new Date(date), "dd/MM/yyyy", { locale: ptBR });
};

/**
 * Convert interval string to Portuguese
 * @param interval Subscription interval
 * @returns Localized interval string
 */
export const formatInterval = (interval: string): string => {
  switch (interval) {
    case "monthly":
      return "mês";
    case "yearly":
      return "ano";
    default:
      return interval;
  }
};
