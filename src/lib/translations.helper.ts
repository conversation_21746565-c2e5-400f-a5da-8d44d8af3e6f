export const translations = {
  pt: {
    payment: {
      pix: "PIX",
      boleto: "Boleto",
      credit_card: "Cartão de Crédito",
    },
    status: {
      future: "Futuro",
      active: "Ativo",
      canceled: "Cancelado",
      pending: "Pendente",
      overdue: "Vencido",
      trial: "Trial",
      expired: "Expirado",
    },
    interval: {
      monthly: "Mensal",
      yearly: "Anual",
    },
    invoice: {
      status: {
        paid: "Pago",
        pending: "Pendente",
        canceled: "Cancelado",
        overdue: "Vencido",
      },
    },
    common: {
      remainingInstallments: "Restam {count} parcelas",
      upgrade: "Upgrade",
      partialUpgrade: "Upgrade",
      downgrade: "Downgrade",
      select: "Se<PERSON>cion<PERSON>",
      selected: "Selecionado",
    },
  },
};

export const getTranslation = (key: string, lang: string = "pt") => {
  const keys = key.split(".");
  let translation = translations[lang];
  for (const k of keys) {
    translation = translation[k];
    if (!translation) {
      return null;
    }
  }
  return translation;
};

export const t = getTranslation;
