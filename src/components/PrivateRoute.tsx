import { useAuth } from "@/contexts/auth/useAuth";
import { Navigate } from "react-router-dom";
import { ReactNode } from "react";
import { Loading } from "./Loading";

interface PrivateRouteProps {
  children: ReactNode;
}

export const PrivateRoute = ({ children }: PrivateRouteProps) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return <Loading className="w-full min-h-screen h-full flex items-center justify-center" />;
  }

  // Redirecionar para a página inicial se não estiver autenticado
  if (isAuthenticated === false && !loading) {
    return <Navigate to="/" replace />;
  }

  // Se estiver autenticado, renderizar o componente filho
  return <>{children}</>;
};
