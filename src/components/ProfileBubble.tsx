import { useAuth } from "@/contexts/auth/useAuth";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { LogOut, Notebook, User } from "lucide-react";
import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Separator } from "./ui/separator";

interface ProfileBubbleProps {
  mobile?: boolean;
}

const ProfileBubble: React.FC<ProfileBubbleProps> = ({ mobile = false }) => {
  const { resetCheckout } = useCheckout();
  const { user, displayName, signOut, shortenDisplayName } = useAuth();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);

  const handleSignOut = () => {
    signOut();
    setIsProfileMenuOpen(false);
    resetCheckout();
  };

  const location = useLocation();
  const route = location.pathname;

  if (mobile) {
    return (
      <div className="">
        <div className="flex items-center gap-2 w-full text-sm text-gray-700 dark:text-gray-300">
          {user?.photoURL ? (
            <img
              src={user.photoURL}
              alt="Foto de perfil"
              className="h-8 w-8 rounded-full object-cover"
            />
          ) : (
            <div className="h-8 w-8 rounded-full flex items-center justify-center">
              <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
            </div>
          )}

          <span className="text-sm text-gray-700 dark:text-gray-300">
            {shortenDisplayName()}
          </span>
        </div>
        <div className="py-1">
          {route !== "/subscription" && (
            <Link
              to="/subscription"
              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="Ir para assinatura"
            >
              <Notebook className="h-4 w-4" />
              Assinatura
            </Link>
          )}
          {route !== "/" && (
            <Link
              to="/"
              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              title="Ver planos disponíveis"
            >
              <Notebook className="h-4 w-4" />
              Planos
            </Link>
          )}
          <button
            onClick={handleSignOut}
            className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          >
            <LogOut className="h-4 w-4" />
            Sair
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center p-2"
        title={`Você está logado como ${displayName}`}
        onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
      >
        {user?.photoURL ? (
          <img
            src={user.photoURL}
            alt="Foto de perfil"
            className="h-8 w-8 rounded-full object-cover"
          />
        ) : (
          <div className="h-8 w-8 flex items-center justify-center">
            <User className="h-5 w-5 text-gray-500 dark:text-gray-400 hover:scale-110 transition-transform duration-300" />
          </div>
        )}
      </button>

      {/* Profile Dropdown Menu */}
      {isProfileMenuOpen && (
        <div className="absolute right-0 mt-2 min-w-48 rounded-md shadow-lg bg-white dark:bg-slate-900 z-50">
          <div className="py-1">
            <div className="flex flex-col gap-1 px-4 py-2">
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {shortenDisplayName()}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {user.email}
              </span>
            </div>

            <Separator />

            {route !== "/subscription" && (
              <Link
                to="/subscription"
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Ir para assinatura"
              >
                <User className="h-4 w-4" />
                Assinatura
              </Link>
            )}
            {route !== "/" && (
              <Link
                to="/"
                className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                title="Ver planos disponíveis"
              >
                <Notebook className="h-4 w-4" />
                Planos
              </Link>
            )}
            <button
              onClick={handleSignOut}
              className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <LogOut className="h-4 w-4" />
              Sair
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileBubble;
