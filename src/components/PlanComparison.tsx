import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Dialog<PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Plan } from "@/types/plan-types";
import { Check, X } from "lucide-react";

export function PlanComparison({ plans }: { plans: Plan[] }) {
  const plansNames = plans.map(plan => plan.name)

  const featuresComparison = plans.reduce((acc, { name: planName, features }) => {
    features.forEach(({ name, included }) => {
      if (!acc[name]) {
        acc[name] = {}
        plansNames.forEach(planName => (acc[name][planName] = false))
      }
      acc[name][planName] = included
    })

    return acc
  }, {})

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full mt-4">
          Comparar <PERSON>dos os Planos
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-auto custom-scrollbar" aria-describedby={undefined}>
        <DialogHeader>
          <DialogTitle>Comparativo Detalhado dos Planos</DialogTitle>
        </DialogHeader>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[300px]">Recurso</TableHead>
              {plansNames.map(planName => (
                <TableHead key={planName}>{planName}</TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {Object.entries(featuresComparison).map(([featureName, values]) => (
              <TableRow key={featureName}>
                <TableCell className="font-medium">{featureName}</TableCell>
                {plansNames.map(planName => (
                  <TableCell key={planName}>
                    {typeof values[planName] === "boolean" ? (
                      values[planName] ? (
                        <Check className="h-4 w-4 text-[#0071e2]" />
                      ) : (
                        <X className="h-4 w-4 text-gray-400" />
                      )
                    ) : (
                      values[planName]
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </DialogContent>
    </Dialog>
  );
}