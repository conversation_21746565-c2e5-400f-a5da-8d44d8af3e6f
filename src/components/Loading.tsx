import { Discuss } from "react-loader-spinner";

interface LoadingProps {
  className?: string;
}

export const Loading = ({ className }: LoadingProps) => (
  <div className={className}>
    <Discuss
      visible={true}
      // height="80"
      // width="80"
      ariaLabel="Loading Spinner"
      // wrapperStyle={{}}
      // wrapperClass="scale-x-[-1] rotate-45"
      wrapperClass={"rotate-[-135deg]"}
      colors={["#c60060", "#c60060"]}
    />
  </div>
);
