"use client";

import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import * as React from "react";

import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { cn } from "@/lib/utils";
import { ptBR } from "date-fns/locale";
import { useEffect } from "react";
import { InputMessageError } from "../InputMessageError";
import { Label } from "../ui/label";

export interface DatePickerProps {
  minDateYear: number;
  errors;
}

export function DatePicker({ minDateYear, errors }: DatePickerProps) {
  const { form } = useCheckout();
  const birthdate = form.getValues("birthdate");
  const [date, setDate] = React.useState<Date>(
    birthdate ? new Date(birthdate) : undefined
  );
  const today = new Date();
  const minDate = new Date();
  minDate.setFullYear(today.getFullYear() - minDateYear);

  const updateValue = (value: Date) => {
    const birthdate = format(value, "MM/dd/yy");
    form.setValue("birthdate", birthdate, { shouldValidate: true });
    setDate(value);
  };

  useEffect(() => {
    form.register("birthdate", {
      required: {
        value: true,
        message: "Data de nascimento é obrigatória",
      },
    });
  }, [form]);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div className="flex flex-col gap-1">
          <Label htmlFor="date" className="mt-2">
            Data de Nascimento
          </Label>
          <div className="relative">
            <Button
              variant={"outline"}
              className={cn(
                "w-full h-[40px] px-3 py-2 text-left border border-gray-300 rounded-md bg-white shadow-sm",
                "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                !date && "text-gray-500"
              )}
            >
              <CalendarIcon className="mr-2 h-5 w-5 text-gray-400" />
              {date ? (
                format(date, "PPP", { locale: ptBR })
              ) : (
                <span>Selecione uma data</span>
              )}
            </Button>
            <InputMessageError error={errors.birthdate?.message} />
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-auto" align="start">
        <Calendar
          mode="single"
          selected={date}
          onSelect={(e) => updateValue(e)}
          initialFocus
          defaultMonth={minDate}
          disabled={(date) => date > minDate || date < new Date("1900-01-01")}
        />
      </PopoverContent>
    </Popover>
  );
}
