import { CheckoutFormData } from '@/components/checkout/types';
import { FieldErrors, UseFormRegister } from 'react-hook-form';
import { InputMessageError } from '../InputMessageError';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

interface EmailInputProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
  disabled?: boolean;
}

export const EmailInput = ({ register, errors, disabled }: EmailInputProps) => (
  <div>
    <Label htmlFor="email">E-mail</Label>
    <Input
      // className={`input-class ${errors.email ? 'border-red-500' : ''}`}
      id="email"
      type="email"
      {...register("email", {
        pattern: {
          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
          message: "E-mail inválido"
        },
        required: {
          value: true,
          message: "E-mail é obrigatório"
        }
      })}
      placeholder="<EMAIL>"
      disabled={disabled}
    />
    <InputMessageError error={errors.email?.message} />
  </div>
);
