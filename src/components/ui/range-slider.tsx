import { cn } from "@/lib/utils"
import * as SliderPrimitive from "@radix-ui/react-slider"
import * as React from "react"

interface Range {
  min: number
  max: number
}

interface RangeSliderProps extends React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root> {
  min: number
  max: number
  value: number[]
  ranges: Range[]
  defaultRange?: Range
  onSliderChange: (values: number[]) => void
  formatValue?: (value: number) => string
  className?: string
  disabled?: boolean
}

const RangeSlider = React.forwardRef<
  React.ElementRef<typeof SliderPrimitive.Root>,
  RangeSliderProps
>(({
  className,
  min,
  max,
  value,
  ranges,
  defaultRange,
  onSliderChange,
  formatValue = (value) => value.toString(),
  disabled = false,
  ...props
}, ref) => {

  return (
    <div className="space-y-4">
      <SliderPrimitive.Root
        ref={ref}
        min={min}
        max={max}
        value={value}
        step={1}
        minStepsBetweenThumbs={1}
        onValueChange={onSliderChange}
        disabled={disabled || min >= max}
        className={cn(
          "relative flex w-full touch-none select-none items-center",
          className
        )}
        {...props}
      >
        <SliderPrimitive.Track className="relative h-2 w-full grow overflow-hidden rounded-full bg-secondary">
          <SliderPrimitive.Range className="absolute h-full bg-gradient-to-l from-blue-600 to-cyan-500" />
        </SliderPrimitive.Track>

        {/* Range markers */}
        {ranges.map((range, index) => (
          <div key={index} className="absolute h-1.5" style={{ left: `calc(${((range.min - min) / (max - min)) * 100}%)`, width: `calc(${((range.max - range.min) / (max - min)) * 100}%)`, top: '0' }}>
            <div className="h-2 w-0.5 bg-gray-200" />
          </div>
        ))}

        <SliderPrimitive.Thumb className="block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" />

      </SliderPrimitive.Root>
    </div>
  )
})

RangeSlider.displayName = "RangeSlider"

export { RangeSlider }
