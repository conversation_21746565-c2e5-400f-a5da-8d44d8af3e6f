import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON>alogClose,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { calculateYearlyDiscount, formatCurrency } from "@/lib/utils";
import { PlanModel } from "@/models/Plan";
import { Plan } from "@/types/plan-types";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Slider } from "../ui/slider";
import { getBasePrice } from "@/lib/plan.utils";

type PlanExtrasProps = {
  isYearly: boolean;
  isOpen: boolean;
  onClose: () => void;
  plan: Plan | null;
  setPlan: (plan: Plan | null) => void;
};

export const PlanExtras = ({
  isYearly = false,
  isOpen,
  onClose,
  plan,
  setPlan,
}: PlanExtrasProps) => {
  const [customPlan, setCustomPlan] = useState<Plan>(plan);
  const [maxDiscount, setMaxDiscount] = useState(0);

  useEffect(() => {
    setCustomPlan(plan);
  }, [plan]);

  useEffect(() => {
    const discount = customPlan.customFeatures.reduce((acc, feature) => {
      const value = feature.yearlyPrice;
      const discount = feature.monthlyPrice - feature.yearlyPrice;
      const savings = calculateYearlyDiscount(value, discount);
      if (savings > acc) {
        acc = savings;
      }
      return acc;
    }, 0);
    setMaxDiscount(discount);
  }, [customPlan]);

  const confirmChanges = () => {
    setPlan(PlanModel.recriate(customPlan));
    onClose();
  };

  const handleChange = (id: string, value: number) => {
    const customFeatures = customPlan.customFeatures.map((feature) => {
      const validValue = Math.max(feature.included, Math.min(Infinity, value));
      if (feature.id === id) {
        return {
          ...feature,
          quantity: validValue,
        };
      }
      return feature;
    });
    setCustomPlan(
      PlanModel.recriate({
        ...customPlan,
        customFeatures: customFeatures,
      })
    );
  };

  const calculatedList = customPlan.customFeatures.map((feature) => {
    const quantity = Math.max(feature.included, Math.min(Infinity, feature.quantity));
    return {
      ...feature,
      additionals: feature.quantity - feature.included,
      quantity,
      totalPrice:
        (isYearly ? feature.yearlyPrice : feature.monthlyPrice) *
        (feature.quantity - feature.included),
    };
  });

  const totalCustomPrice = calculatedList
    .filter((f) => f.additionals > 0)
    .reduce(
      (acc, { totalPrice }) => acc + totalPrice,
      0
    );
  const planPrice = getBasePrice(customPlan) / (isYearly ? 12 : 1);
  const total = planPrice + totalCustomPrice;
  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) onClose();
      }}
    >
      <DialogContent className="max-w-2xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border-0 shadow-2xl">
        <DialogHeader className="mb-6">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <DialogTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-500">
              Personalizar Plano {plan?.name}
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300 mt-2">
              Ajuste as características do seu plano de acordo com suas
              necessidades
            </DialogDescription>
          </motion.div>
        </DialogHeader>

        {maxDiscount > 0 && (
          <div className="absolute -top-3 right-4 flex items-center gap-1 bg-orange-500 dark:bg-gradient-to-r dark:from-orange-900 dark:to-orange-600 px-3 py-1 rounded-full text-white text-xs font-medium shadow-md">
            🔥&nbsp;Até {maxDiscount.toFixed(0)}% de economia no plano anual
          </div>
        )}
        <div className="space-y-8 max-h-[50vh] overflow-auto">
          {calculatedList.map(
            ({ id, name, totalPrice, quantity, included }, index) => {
              return (
                <motion.div
                  key={id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="p-4 rounded-xl shadow-sm hover:shadow-md transition-all"
                >
                  <div className="flex flex-col space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <Label
                          htmlFor={name}
                          className="text-lg font-medium text-gray-700 dark:text-gray-200"
                        >
                          {name}
                        </Label>
                        <div className="flex">
                          {totalPrice > 0 && (
                            <p className=" text-sm text-green-600 font-medium mr-4">
                              +{formatCurrency(totalPrice)}/mês
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="relative">
                        <Input
                          id={name}
                          type="number"
                          min={included}
                          value={quantity}
                          onChange={(e) =>
                            handleChange(id, Number(e.target.value))
                          }
                          onKeyDown={(e) => {
                            if (e.key === "-" || e.key === "e") {
                              e.preventDefault();
                            }
                          }}
                          className="w-24 text-center font-semibold border-2 focus:ring-2 focus:ring-blue-500"
                        />
                      </div>
                    </div>

                    <Slider
                      min={included}
                      max={100}
                      value={[quantity]}
                      onValueChange={(value) => handleChange(id, value[0])}
                    />
                  </div>
                </motion.div>
              );
            }
          )}
        </div>
        <div className="divider h-0.5 bg-gray-200 dark:bg-gray-700"></div>
        <div className="flex flex-col px-4 py-2">
          <div className="flex justify-between items-center">
            <div className="text-lg font-medium text-gray-700 dark:text-gray-200">
              {plan.name}
            </div>
            <div className="text-right">
              <p className="relative text-md text-green-600 font-medium">
                {formatCurrency(planPrice)}
                <span className="text-xs">
                  /mês
                </span>
              </p>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="text-lg font-medium text-gray-700 dark:text-gray-200">
              Extras
            </div>
            <div className="text-right">
              <p className="relative text-md text-green-600 font-medium">
                +{formatCurrency(totalCustomPrice)}
                <span className="text-xs">
                  /mês
                </span>
              </p>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex-1 text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-500">
              Total
            </div>
            <div className="text-right">
              <p className="relative text-md text-green-600 font-medium">
                {formatCurrency(total)}
                <span className="text-xs">
                  /mês
                </span>
              </p>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-8">
          <div className="flex space-x-4">
            <DialogClose asChild>
              <Button
                type="button"
                variant="outline"
                className="px-6 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => {
                  setCustomPlan(plan);
                  onClose();
                }}
              >
                Cancelar
              </Button>
            </DialogClose>
            <Button
              type="submit"
              className="px-6 py-2 bg-gradient-to-r from-blue-600 to-cyan-500 text-white hover:opacity-90 transition-opacity "
              onClick={confirmChanges}
            >
              Aplicar Alterações
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
