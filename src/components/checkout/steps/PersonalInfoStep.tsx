import { FieldErrors, UseFormRegister } from "react-hook-form";
import { PersonalInfoFields } from "../PersonalInfoFields";
import { CheckoutFormData } from "../types";

interface PersonalInfoStepProps {
  title: string;
  isCompany: boolean;
  watch;
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export function PersonalInfoStep({ title, register, watch, errors }: PersonalInfoStepProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{title}</h3>
      <PersonalInfoFields register={register} errors={errors} watch={watch} />
    </div>
  );
}