import { FieldErrors, UseFormRegister } from "react-hook-form";
import { CompanyInfoFields } from "../CompanyInfoFields";
import { CheckoutFormData } from "../types";

interface CompanyInfoStepProps {
  title: string;
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export function CompanyInfoStep({ title, register, errors }: CompanyInfoStepProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{title}</h3>
      <CompanyInfoFields register={register} errors={errors} />
    </div>
  );
}