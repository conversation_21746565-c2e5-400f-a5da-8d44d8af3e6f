import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { FieldErrors, UseFormRegister } from "react-hook-form";
import { CNPJInput } from "../forms/CNPJInput";
import { PhoneInput } from "../forms/PhoneInput";
import { InputMessageError } from "../InputMessageError";
import { CheckoutFormData } from "./types";

interface CompanyInfoFieldsProps {
  register: UseFormRegister<CheckoutFormData>;
  errors: FieldErrors<CheckoutFormData>;
}

export function CompanyInfoFields({ register, errors }: CompanyInfoFieldsProps) {
  const { countryCodes, selectedCountry, setSelectedCountryCode } = useCheckout();

  return (
    <div className="grid gap-4">
      <div>
        <Label htmlFor="companyName">Nome da Empresa</Label>
        <Input
          id="companyName"
          {...register("companyName", {
            required: {
              value: true,
              message: "Nome da empresa é obrigatório"
            }
          })}
        />
        <InputMessageError error={errors.companyName?.message} />
      </div>

      <CNPJInput register={register} errors={errors} />
      <PhoneInput
        selectId="phoneCountryCode"
        inputId="companyPhone"
        type="phone"
        selectedCountry={selectedCountry}
        countryCodes={countryCodes}
        setSelectedCountryCode={setSelectedCountryCode}
        register={register}
        errors={errors}
      />

      <div>
        <Label htmlFor="companyEmail">E-mail Comercial</Label>
        <Input
          id="companyEmail"
          type="email"
          {...register("companyEmail", {
            pattern: {
              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
              message: "E-mail inválido"
            },
            required: {
              value: true,
              message: "E-mail é obrigatório"
            }
          })}
        />
        <InputMessageError error={errors.companyEmail?.message} />
      </div>
    </div>
  );
}