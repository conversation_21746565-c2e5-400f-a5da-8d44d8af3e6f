import { Button } from "@/components/ui/button";
interface CheckoutNavigationProps {
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  onSubmit?: () => void;
  isNextDisabled?: boolean;
}

export function CheckoutNavigation({
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSubmit,
  isNextDisabled,
}: CheckoutNavigationProps) {
  return (
    <div className="flex gap-4">
      <Button
        type="button"
        variant="outline"
        onClick={onPrevious}
        className="w-1/3 rounded-full border-[#0071e2] text-[#0071e2] hover:bg-[#0071e2]/10"
      >
        {currentStep === 1 ? "Voltar" : "Anterior"}
      </Button>
      {currentStep === totalSteps ? (
        <Button
          type="button"
          onClick={onSubmit}
          className="w-2/3 rounded-full bg-[#0071e2] hover:bg-[#0071e2]/90"
          disabled={isNextDisabled}
        >
          Finalizar Compra
        </Button>
      ) : (
        <Button
          type="button"
          onClick={onNext}
          className="w-2/3 rounded-full bg-[#0071e2] hover:bg-[#0071e2]/90"
          disabled={isNextDisabled}
        >
          Próximo
        </Button>
      )}
    </div>
  );
}