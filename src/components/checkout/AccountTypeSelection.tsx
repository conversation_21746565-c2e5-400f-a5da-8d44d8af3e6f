import { Button } from "@/components/ui/button";
import { Building2, User } from "lucide-react";

interface AccountTypeSelectionProps {
  title: string;
  isCompany: boolean | null;
  disabled: boolean;
  onTypeChange: (value: boolean) => void;
}

export function AccountTypeSelection({ title, isCompany, onTypeChange, disabled }: AccountTypeSelectionProps) {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">{title}</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Button
          type="button"
          variant={isCompany === false ? "default" : "outline"}
          className="h-32 flex flex-col items-center justify-center gap-2"
          onClick={() => onTypeChange(false)}
          disabled={disabled}
        >
          <User className="h-8 w-8" />
          <span>Pessoa Física</span>
        </Button>
        <Button
          type="button"
          variant={isCompany === true ? "default" : "outline"}
          className="h-32 flex flex-col items-center justify-center gap-2"
          onClick={() => onTypeChange(true)}
          disabled={disabled}
        >
          <Building2 className="h-8 w-8" />
          <span>Pessoa Jurídica</span>
        </Button>
      </div>
    </div>
  );
}