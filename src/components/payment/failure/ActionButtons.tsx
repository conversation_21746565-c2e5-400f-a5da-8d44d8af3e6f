import { Button } from "@/components/ui/button";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { motion } from "framer-motion";

interface ActionButtonsProps {
    itemAnimation: any;
}

export const ActionButtons = ({ itemAnimation }: ActionButtonsProps) => {
    const { paySubscription } = useCheckout();
    return (
        <motion.div
            variants={itemAnimation}
            className="flex flex-col sm:flex-row gap-3 justify-center"
        >
            <Button
                variant="outline"
                onClick={paySubscription}
                className="w-full sm:w-auto"
            >
                Tentar novamente
            </Button>
        </motion.div>
    );
}; 