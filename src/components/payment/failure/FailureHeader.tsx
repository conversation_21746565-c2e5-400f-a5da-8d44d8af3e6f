import { motion } from "framer-motion";

interface FailureHeaderProps {
    itemAnimation: any;
    errorMessage?: string;
}

export const FailureHeader = ({ itemAnimation, errorMessage }: FailureHeaderProps) => {
    return (
        <motion.div variants={itemAnimation} className="space-y-2">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {errorMessage || "Ops! Algo deu errado."}
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
                Não foi possível processar sua compra. Por favor, tente novamente.
            </p>
        </motion.div>
    );
}; 