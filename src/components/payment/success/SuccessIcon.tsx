import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";

const pathAnimation = {
  hidden: { pathLength: 0, fill: "rgba(233, 23, 124, 0)" },
  visible: {
    pathLength: 1,
    fill: "rgba(233, 23, 124, 0.2)",
    transition: {
      pathLength: { duration: 1.5, ease: "easeInOut" },
      fill: { duration: 1.5, delay: 0.5, ease: "easeInOut" },
    },
  },
};

const checkAnimation = {
  hidden: { pathLength: 0 },
  visible: {
    pathLength: 1,
    transition: {
      delay: 1.5,
      duration: 1,
      ease: "easeInOut",
    },
  },
};

export const SuccessIcon = () => {
  return (
    <motion.div
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ duration: 0.5 }}
      className="relative inline-block"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-[#e9177c]/30 to-[#f16434]/30 rounded-full blur-2xl transform -translate-y-1"></div>
      <motion.div initial="hidden" animate="visible" className="relative z-10">
        <CheckCircle
          className="w-16 h-16 text-transparent bg-gradient-to-r from-[#e9177c] to-[#f16434] bg-clip-text mx-auto"
          strokeWidth={2}
          style={{
            fill: "rgba(233, 23, 124, 0)",
            stroke: "url(#gradient)",
          }}
        />
        <svg width="0" height="0">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#e9177c" />
              <stop offset="100%" stopColor="#f16434" />
            </linearGradient>
          </defs>
        </svg>
        <motion.circle
          cx="12"
          cy="12"
          r="10"
          className="absolute inset-0"
          style={{
            fill: "url(#gradient)",
            scale: 2.4,
          }}
          initial={{ scale: 2.4, fillOpacity: 0 }}
          animate={{ fillOpacity: 0.2 }}
          transition={{ duration: 1.5, delay: 0.5 }}
        />
        <motion.path
          d="M8 12L11 15L16 9"
          stroke="url(#gradient)"
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
          fill="none"
          style={{
            scale: 2.4,
            transformOrigin: "center",
          }}
          variants={checkAnimation}
        />
      </motion.div>
    </motion.div>
  );
};
