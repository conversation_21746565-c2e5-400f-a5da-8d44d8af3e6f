import { motion } from "framer-motion";

interface ProcessingHeaderProps {
    itemAnimation: any;
}

export const ProcessingHeader = ({ itemAnimation }: ProcessingHeaderProps) => {
    return (
        <motion.div variants={itemAnimation} className="space-y-2">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Processando seu pagamento
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
                Aguarde enquanto confirmamos sua transação
            </p>
        </motion.div>
    );
}; 