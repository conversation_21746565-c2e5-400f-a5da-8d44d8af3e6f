import { ActionButtons } from "@/components/payment/success/ActionButtons";
import { BenefitCard } from "@/components/payment/success/BenefitCard";
import { SubscriptionDetails } from "@/components/payment/success/SubscriptionDetails";
import { SuccessHeader } from "@/components/payment/success/SuccessHeader";
import { SuccessIcon } from "@/components/payment/success/SuccessIcon";
import { SupportFooter } from "@/components/payment/SupportFooter";
import { Card } from "@/components/ui/card";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { useCheckout } from "@/contexts/checkout/useCheckout";
import { motion } from "framer-motion";
import { BENEFITS } from "./checkout/payment/types";

interface PurchaseSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const PurchaseSuccess = ({ isOpen, onClose }: PurchaseSuccessModalProps) => {
  const {
    payment: { subscription },
  } = useCheckout();

  const containerAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.25,
      },
    },
  };

  const itemAnimation = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="w-full h-screen max-w-full p-0 bg-transparent border-none flex items-center justify-center overflow-y-auto custom-scrollbar"
        aria-describedby={undefined}
        showCloseButton={false}
      >
        <style>
          {`
            .dialog-overlay {
              background-color: rgba(255, 255, 255, 0.2) !important;
              backdrop-filter: blur(4px);
            }
          `}
        </style>
        <motion.div
          variants={containerAnimation}
          initial="hidden"
          animate="visible"
          className="w-full h-full relative z-10 flex items-center justify-center"
        >
          <Card className="w-[60%] max-h-[90vh] overflow-y-auto p-8 space-y-4 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl border border-slate-200/50 shadow-2xl">
            <DialogTitle>
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="flex justify-center"
              >
                <img
                  src="/qi-plus-brand.png"
                  alt="QI PLUS Logo"
                  className="h-14 w-auto px-4 py-2"
                />
              </motion.div>
            </DialogTitle>

            <div className="text-center space-y-2">
              <SuccessIcon />
              <SuccessHeader itemAnimation={itemAnimation} />
            </div>

            <motion.div
              variants={itemAnimation}
              className="grid grid-cols-1 md:grid-cols-3 gap-4"
            >
              {BENEFITS.map((benefit) => (
                <BenefitCard key={benefit.title} {...benefit} />
              ))}
            </motion.div>

            {subscription && (
              <SubscriptionDetails subscription={subscription} />
            )}

            <ActionButtons itemAnimation={itemAnimation} onClose={onClose} />
            <SupportFooter itemAnimation={itemAnimation} />
          </Card>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};

export default PurchaseSuccess;
