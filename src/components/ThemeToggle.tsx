import { useTheme } from "@/contexts/theme/ThemeContext";
import { cn } from "@/lib/utils";
import { Monitor, Moon, Sun } from "lucide-react";
import { useState } from "react";

export default function ThemeToggle({ isMobile = false }) {
  const { theme, setTheme } = useTheme();
  const [isThemeMenuOpen, setIsThemeMenuOpen] = useState(false);

  const themeOptions = [
    { value: "light", label: "Claro", icon: Sun },
    { value: "dark", label: "Escuro", icon: Moon },
    { value: "system", label: "Siste<PERSON>", icon: Monitor },
  ];

  const currentTheme = themeOptions.find((option) => option.value === theme);
  const ThemeIcon = currentTheme?.icon || Sun;

  return (
    <div className="relative">
      <button
        type="button"
        className="flex items-center gap-2 p-2 rounded-full transition-colors"
        title="Alterar tema do sistema"
        onClick={() => setIsThemeMenuOpen(!isThemeMenuOpen)}
      >
        <div className="h-8 w-8 rounded-full flex items-center justify-center">
          <ThemeIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 hover:scale-110 transition-transform duration-300" />
        </div>
        {isMobile && <span className="text-sm">Tema</span>}
      </button>

      {isThemeMenuOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-900 ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1">
            {themeOptions.map(({ value, label, icon: Icon }) => (
              <button
                key={value}
                onClick={() => {
                  setTheme(value as "light" | "dark" | "system");
                  setIsThemeMenuOpen(false);
                }}
                className={cn(
                  "flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",
                  theme === value && "bg-gray-100 dark:bg-gray-800"
                )}
              >
                <Icon className="h-4 w-4" />
                {label}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
