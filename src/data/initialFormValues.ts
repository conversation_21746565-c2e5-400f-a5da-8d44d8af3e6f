import { CheckoutFormData, PaymentMethod } from "@/components/checkout/types";
import { installments } from "@/config/payment";

export const initialFormValues = {
  country: "BR", // Dados do cliente
  phoneCountryCode: "+55",
  billingCountry: "BR", // Dados do endereço de fatura
  paymentMethod: PaymentMethod.PIX,
  isCompany: false,
  discount: 0,
  companyPhoneCountryCode: "+55",
  installments: installments[0].toString(),
  acceptTerms: false,
  billingDay: 10
} as Partial<CheckoutFormData>;
