import { Timestamp } from "firebase/firestore";

// Enum para representar os status possíveis
enum SubscriptionStatus {
  ACTIVE = "active",
  FAILED = "failed",
  FUTURE = "future",
}

export class Cycle {
  id: string;
  start_at: string;
  end_at: string;
  billing_at: string;
  status: string;
  cycle: number;

  constructor(data) {
    console.log("data", data);
    this.id = data.id;
    this.start_at = data.start_at;
    this.end_at = data.end_at;
    this.billing_at = data.billing_at;
    this.status = data.status;
    this.cycle = data.cycle;
  }
}

// Classe base para assinatura
export class Subscription {
  id: string;
  code: string;
  status: SubscriptionStatus;
  paymentMethod: string;
  customerName: string;
  planName: string;
  currentCycle?: Cycle;
  paymentStatus: "paid" | "failed";
  metadata: Record<string, any>;
  isUpgrade?: boolean;
  constructor(data) {
    const { id, code, status, payment_method, customer, items } = data;
    this.id = id;
    this.code = code;
    this.status = status;
    this.paymentMethod = payment_method;
    this.customerName = customer.name;
    this.planName = items[0].name;
    this.currentCycle = data.current_cycle && new Cycle(data.current_cycle);
    this.isUpgrade = data.isUpgrade || false;
  }

  // Método para verificar se a assinatura foi bem-sucedida
  isSuccessful(): boolean {
    return this.status === SubscriptionStatus.ACTIVE;
  }

  // Método para gerar a mensagem de feedback para o usuário
  getFeedbackMessage(): string {
    if (this.isSuccessful()) {
      return `Parabéns, ${this.customerName}! Sua assinatura do plano "${this.planName}" foi ativada com sucesso!.`;
    } else {
      return `Infelizmente, houve uma falha no processamento da assinatura "${this.planName}" para ${this.customerName}.`;
    }
  }

  updatePaymentStatus(status: "paid" | "failed") {
    this.paymentStatus = status;
  }
}

// Classe para assinatura via boleto
export class BoletoSubscription extends Subscription {
  boletoId?: string;
  boletoUrl?: string;

  constructor(data) {
    try {
      super(data);
      if (data.boleto) {
        this.boletoId = data.boleto.id;
        this.boletoUrl = data.boleto.url;
      }
    } catch (error) {
      console.error("Error on BoletoSubscription", error);
    }
  }

  // Sobrescrevendo o método para incluir o link do boleto
  getFeedbackMessage(): string {
    const baseMessage = super.getFeedbackMessage();
    if (!this.isSuccessful() && this.boletoUrl) {
      return `${baseMessage} Você pode tentar novamente acessando o boleto aqui: ${this.boletoUrl}`;
    }
    return baseMessage;
  }
}

// Classe para assinatura via cartão de crédito
export class CreditCardSubscription extends Subscription {
  cardLastFour: string;

  constructor(data) {
    super(data);
    this.cardLastFour = data.card ? data.card.last_four_digits : "XXXX";
  }

  // Sobrescrevendo o método para incluir detalhes do cartão
  getFeedbackMessage(): string {
    const baseMessage = super.getFeedbackMessage();
    return `${baseMessage} (Cartão final: ${this.cardLastFour})`;
  }
}
